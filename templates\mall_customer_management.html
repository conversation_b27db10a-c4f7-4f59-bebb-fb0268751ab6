<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 商场客户管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #007BFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
        }
        .staff-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        .staff-info span {
            margin-right: 15px;
            color: white;
        }
        .logout-btn {
            color: #fff;
            text-decoration: none;
            font-weight: 500;
        }
        .logout-btn:hover {
            color: #f8f9fa;
            text-decoration: underline;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        .page-title {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-title h2 {
            margin: 0;
            color: #333;
        }
        .search-bar {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .search-bar input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 300px;
        }
        .search-bar select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 150px;
        }
        .search-bar button {
            padding: 8px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .customer-list {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 4px;
        }
        .customer-list th, .customer-list td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .customer-list th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .customer-list tbody tr:hover {
            background-color: #f9f9f9;
        }
        .customer-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
        }
        .status-active {
            background-color: #e6fff0;
            color: #52c41a;
        }
        .status-paused {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        .status-terminated {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .billing-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
        }
        .billing-paid {
            background-color: #e6fff0;
            color: #52c41a;
        }
        .billing-unpaid {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .billing-partial {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
            font-size: 0.85rem;
        }
        .view-btn {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .edit-btn {
            background-color: #fff8e6;
            color: #f5a623;
        }
        .delete-btn {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .modal-header {
            background-color: #007BFF;
            color: white;
        }
        .modal-footer {
            border-top: 1px solid #eee;
            padding: 15px;
        }
        .form-group label {
            font-weight: bold;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination button {
            padding: 8px 15px;
            margin: 0 5px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #007BFF;
            color: white;
            border-color: #007BFF;
        }
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .nav-tabs {
            margin-bottom: 20px;
        }
        .tab-pane {
            padding: 20px;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        .badge-discount {
            background-color: #e6f7ff;
            color: #1890ff;
            font-size: 0.85rem;
            padding: 5px 8px;
            border-radius: 12px;
        }
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
        .billing-status.status-unpaid {
            background-color: #ffc107;
        }
        .billing-status.status-partial {
            background-color: #17a2b8;
        }
        .billing-status.status-paid {
            background-color: #28a745;
        }

        /* 账单订单详情样式 */
        .bill-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .bill-info h6 {
            color: #007bff;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .order-list h6, .order-detail h6 {
            color: #495057;
            margin-bottom: 15px;
            font-weight: bold;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }

        .order-detail {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }

        #billOrdersModal .modal-xl {
            max-width: 90%;
        }

        @media (max-width: 768px) {
            #billOrdersModal .modal-xl {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Soulweave改衣坊管理系统</h1>
        <div class="header-controls">
            <div class="staff-info">
                <span>欢迎，{{ staff_name }}</span>
                <a href="/logout" class="logout-btn">退出</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-title">
            <h2>商场客户管理</h2>
            <button class="btn btn-primary" data-toggle="modal" data-target="#addCustomerModal">
                <i class="fas fa-plus"></i> 添加商场客户
            </button>
        </div>

        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="搜索商场品牌名称...">
            <select id="statusFilter">
                <option value="">所有状态</option>
                <option value="活跃">活跃</option>
                <option value="暂停">暂停</option>
                <option value="已终止">已终止</option>
            </select>
            <button id="searchBtn">搜索</button>
        </div>

        <table class="customer-list">
            <thead>
                <tr>
                    <th>商场品牌名称</th>
                    <th>联系人</th>
                    <th>联系电话</th>
                    <th>结算周期</th>
                    <th>合同到期日</th>
                    <th>商场状态</th>
                    <th>付款状态</th>
                    <th>折扣数量</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="customerTableBody">
                <!-- 客户列表将通过JavaScript动态加载 -->
            </tbody>
        </table>

        <div class="pagination" id="pagination">
            <!-- 分页控件将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 添加商场客户模态框 -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1" role="dialog" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCustomerModalLabel">添加商场客户</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="mallName" class="required-field">商场品牌名称</label>
                                <input type="text" class="form-control" id="mallName" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="phone">联系电话</label>
                                <input type="text" class="form-control" id="phone">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="address">商场地址</label>
                            <input type="text" class="form-control" id="address">
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="contractStartDate">合同开始日期</label>
                                <input type="text" class="form-control date-picker" id="contractStartDate">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="contractEndDate">合同结束日期</label>
                                <input type="text" class="form-control date-picker" id="contractEndDate">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="billingCycle">结算周期</label>
                                <select class="form-control" id="billingCycle">
                                    <option value="月结">月结</option>
                                    <option value="季度结算">季度结算</option>
                                    <option value="半年结算">半年结算</option>
                                    <option value="年结">年结</option>
                                </select>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="status">商场状态</label>
                                <select class="form-control" id="status">
                                    <option value="活跃">活跃</option>
                                    <option value="暂停">暂停</option>
                                    <option value="已终止">已终止</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="area">所属区域</label>
                                <select class="form-control" id="area">
                                    <!-- 区域选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                        <h5 class="mt-4 mb-3">联系人信息</h5>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="contactName">联系人姓名</label>
                                <input type="text" class="form-control" id="contactName">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="contactPhone">联系人电话</label>
                                <input type="text" class="form-control" id="contactPhone">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="contactPosition">联系人职位</label>
                            <input type="text" class="form-control" id="contactPosition">
                        </div>
                        <div class="form-group">
                            <label for="remarks">备注信息</label>
                            <textarea class="form-control" id="remarks" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveCustomerBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 商场客户详情模态框 -->
    <div class="modal fade" id="customerDetailModal" tabindex="-1" role="dialog" aria-labelledby="customerDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerDetailModalLabel">商场客户详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="customerDetailTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="info-tab" data-toggle="tab" href="#info" role="tab" aria-controls="info" aria-selected="true">基本信息</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="discount-tab" data-toggle="tab" href="#discount" role="tab" aria-controls="discount" aria-selected="false">产品折扣</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="bill-tab" data-toggle="tab" href="#bill" role="tab" aria-controls="bill" aria-selected="false">月度账单</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="history-tab" data-toggle="tab" href="#history" role="tab" aria-controls="history" aria-selected="false">折扣历史</a>
                        </li>
                    </ul>
                    <div class="tab-content" id="customerDetailTabContent">
                        <!-- 基本信息标签页 -->
                        <div class="tab-pane fade show active" id="info" role="tabpanel" aria-labelledby="info-tab">
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <p><strong>商场品牌名称: </strong><span id="detailMallName"></span></p>
                                    <p><strong>联系电话: </strong><span id="detailPhone"></span></p>
                                    <p><strong>商场地址: </strong><span id="detailAddress"></span></p>
                                    <p><strong>合同期限: </strong><span id="detailContractDates"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>结算周期: </strong><span id="detailBillingCycle"></span></p>
                                    <p><strong>商场状态: </strong><span id="detailStatus"></span></p>
                                    <p><strong>所属区域: </strong><span id="detailArea"></span></p>
                                    <p><strong>联系人: </strong><span id="detailContactName"></span></p>
                                    <p><strong>联系人电话: </strong><span id="detailContactPhone"></span></p>
                                    <p><strong>联系人职位: </strong><span id="detailContactPosition"></span></p>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <p><strong>整体折扣率: </strong><span id="detailOverallDiscountRate"></span></p>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <p><strong>备注信息: </strong></p>
                                    <p id="detailRemarks"></p>
                                </div>
                            </div>
                        </div>

                        <!-- 产品折扣标签页 -->
                        <div class="tab-pane fade" id="discount" role="tabpanel" aria-labelledby="discount-tab">
                            <div class="d-flex justify-content-between align-items-center my-3">
                                <h5>产品折扣列表</h5>
                                <button class="btn btn-sm btn-primary" id="addDiscountBtn">添加折扣</button>
                            </div>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>产品名称</th>
                                        <th>产品类型</th>
                                        <th>折扣率</th>
                                        <th>生效日期</th>
                                        <th>失效日期</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="discountTableBody">
                                    <!-- 折扣列表将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 月度账单标签页 -->
                        <div class="tab-pane fade" id="bill" role="tabpanel" aria-labelledby="bill-tab">
                            <div class="d-flex justify-content-between align-items-center my-3">
                                <h5>月度账单列表</h5>
                                <button class="btn btn-sm btn-primary" id="addBillBtn">添加账单</button>
                            </div>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>账单月份</th>
                                        <th>账单周期</th>
                                        <th>订单数量</th>
                                        <th>原始金额</th>
                                        <th>实际金额</th>
                                        <th>折扣金额</th>
                                        <th>应收金额</th>
                                        <th>付款状态</th>
                                        <th>付款日期</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="billTableBody">
                                    <!-- 账单列表将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 折扣历史标签页 -->
                        <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                            <table class="table table-striped mt-3">
                                <thead>
                                    <tr>
                                        <th>产品名称</th>
                                        <th>产品类型</th>
                                        <th>原折扣率</th>
                                        <th>新折扣率</th>
                                        <th>变更日期</th>
                                        <th>变更原因</th>
                                        <th>操作人</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody">
                                    <!-- 历史记录将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="editCustomerBtn">编辑</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑商场客户模态框 -->
    <div class="modal fade" id="editCustomerModal" tabindex="-1" role="dialog" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCustomerModalLabel">编辑商场客户</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editCustomerForm">
                        <input type="hidden" id="editCustomerId">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="editMallName" class="required-field">商场品牌名称</label>
                                <input type="text" class="form-control" id="editMallName" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="editPhone">联系电话</label>
                                <input type="text" class="form-control" id="editPhone">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editAddress">商场地址</label>
                            <input type="text" class="form-control" id="editAddress">
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="editContractStartDate">合同开始日期</label>
                                <input type="text" class="form-control date-picker" id="editContractStartDate">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="editContractEndDate">合同结束日期</label>
                                <input type="text" class="form-control date-picker" id="editContractEndDate">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="editBillingCycle">结算周期</label>
                                <select class="form-control" id="editBillingCycle">
                                    <option value="月结">月结</option>
                                    <option value="季度结算">季度结算</option>
                                    <option value="半年结算">半年结算</option>
                                    <option value="年结">年结</option>
                                </select>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="editStatus">商场状态</label>
                                <select class="form-control" id="editStatus">
                                    <option value="活跃">活跃</option>
                                    <option value="暂停">暂停</option>
                                    <option value="已终止">已终止</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="editArea">所属区域</label>
                                <select class="form-control" id="editArea">
                                    <!-- 区域选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                        <h5 class="mt-4 mb-3">联系人信息</h5>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="editContactName">联系人姓名</label>
                                <input type="text" class="form-control" id="editContactName">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="editContactPhone">联系人电话</label>
                                <input type="text" class="form-control" id="editContactPhone">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editContactPosition">联系人职位</label>
                            <input type="text" class="form-control" id="editContactPosition">
                        </div>
                        <h5 class="mt-4 mb-3">折扣设置</h5>
                        <div class="form-group">
                            <label for="editOverallDiscountRate">整体折扣率</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="editOverallDiscountRate" step="0.01" min="0" max="1" value="1.0">
                                <div class="input-group-append">
                                    <span class="input-group-text">倍</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">例如：0.9表示9折，适用于该客户的所有订单。与单品折扣可叠加使用。</small>
                        </div>
                        <div class="form-group">
                            <label for="editRemarks">备注信息</label>
                            <textarea class="form-control" id="editRemarks" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="updateCustomerBtn">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 产品折扣模态框 -->
    <div class="modal fade" id="discountModal" tabindex="-1" role="dialog" aria-labelledby="discountModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="discountModalLabel">添加产品折扣</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="discountForm">
                        <input type="hidden" id="discountCustomerId">
                        <input type="hidden" id="discountId">
                        <div class="form-group">
                            <label for="productName" class="required-field">产品名称</label>
                            <select class="form-control" id="productName" required>
                                <option value="">请选择产品</option>
                                <!-- 产品选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="productType" class="required-field">产品类型</label>
                            <select class="form-control" id="productType" required>
                                <option value="">请选择类型</option>
                                <option value="上衣">上衣</option>
                                <option value="裤装">裤装</option>
                                <option value="外套">外套</option>
                                <option value="裙装">裙装</option>
                                <option value="套装">套装</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="discountRate" class="required-field">折扣率</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="discountRate" step="0.01" min="0" max="1" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">倍</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">例如：0.8表示8折</small>
                        </div>
                        <div class="form-group">
                            <label for="effectiveDate" class="required-field">生效日期</label>
                            <input type="text" class="form-control date-picker" id="effectiveDate" required>
                        </div>
                        <div class="form-group">
                            <label for="expiryDate">失效日期</label>
                            <input type="text" class="form-control date-picker" id="expiryDate">
                            <small class="form-text text-muted">不填表示长期有效</small>
                        </div>
                        <div class="form-group">
                            <label for="changeReason">变更原因</label>
                            <input type="text" class="form-control" id="changeReason" placeholder="例如：定期调整、促销活动等">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveDiscountBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 月度账单模态框 -->
    <div class="modal fade" id="billModal" tabindex="-1" role="dialog" aria-labelledby="billModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="billModalLabel">添加月度账单</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="billForm">
                        <input type="hidden" id="billCustomerId">
                        <input type="hidden" id="billId">
                        <div class="form-group">
                            <label for="billYearMonth" class="required-field">账单年月</label>
                            <input type="text" class="form-control" id="billYearMonth" placeholder="YYYY-MM" required>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="billStartDate" class="required-field">账单开始日期</label>
                                <input type="text" class="form-control date-picker" id="billStartDate" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="billEndDate" class="required-field">账单结束日期</label>
                                <input type="text" class="form-control date-picker" id="billEndDate" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="orderCount">订单数量</label>
                                <input type="number" class="form-control" id="orderCount" min="0">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="originalAmount">原始金额（折扣前）</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="originalAmount" step="0.01" min="0">
                                    <div class="input-group-append">
                                        <span class="input-group-text">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="totalAmount">实际金额（折扣后）</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="totalAmount" step="0.01" min="0">
                                    <div class="input-group-append">
                                        <span class="input-group-text">元</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="discountAmount">折扣金额</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="discountAmount" step="0.01" min="0">
                                    <div class="input-group-append">
                                        <span class="input-group-text">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="actualAmount">应收金额</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="actualAmount" step="0.01" min="0">
                                    <div class="input-group-append">
                                        <span class="input-group-text">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="paymentStatus">付款状态</label>
                                <select class="form-control" id="paymentStatus">
                                    <option value="未付款">未付款</option>
                                    <option value="部分付款">部分付款</option>
                                    <option value="已付款">已付款</option>
                                </select>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="paymentDate">付款日期</label>
                                <input type="text" class="form-control date-picker" id="paymentDate">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="paymentMethod">付款方式</label>
                            <select class="form-control" id="paymentMethod">
                                <option value="">请选择</option>
                                <option value="银行转账">银行转账</option>
                                <option value="支付宝">支付宝</option>
                                <option value="微信">微信</option>
                                <option value="现金">现金</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="billRemarks">备注信息</label>
                            <textarea class="form-control" id="billRemarks" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveBillBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除这条记录吗？此操作不可逆。</p>
                    <input type="hidden" id="deleteItemId">
                    <input type="hidden" id="deleteItemType">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 账单订单详情模态框 -->
    <div class="modal fade" id="billOrdersModal" tabindex="-1" role="dialog" aria-labelledby="billOrdersModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="billOrdersModalLabel">账单关联订单</h5>
                    <div class="d-flex">
                        <button type="button" class="btn btn-success btn-sm mr-2" id="exportBillOrdersBtn">
                            <i class="fas fa-file-excel"></i> 导出Excel
                        </button>
                        <button type="button" class="close" data-dismiss="modal" aria-label="关闭">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="bill-info mb-4">
                        <h6>账单信息</h6>
                        <div class="row">
                            <div class="col-md-2">
                                <p><strong>账单周期：</strong><span id="orderBillPeriod"></span></p>
                            </div>
                            <div class="col-md-2">
                                <p><strong>订单数量：</strong><span id="orderBillCount"></span></p>
                            </div>
                            <div class="col-md-2">
                                <p><strong>原始金额：</strong><span id="orderBillOriginalAmount"></span></p>
                            </div>
                            <div class="col-md-2">
                                <p><strong>实际金额：</strong><span id="orderBillAmount"></span></p>
                            </div>
                            <div class="col-md-2">
                                <p><strong>折扣金额：</strong><span id="orderBillDiscount"></span></p>
                            </div>
                            <div class="col-md-2">
                                <p><strong>应收金额：</strong><span id="orderBillActualAmount"></span></p>
                            </div>
                        </div>
                    </div>

                    <div class="order-list">
                        <h6>订单列表</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>创建时间</th>
                                        <th>状态</th>
                                        <th>付款状态</th>
                                        <th>衣物数量</th>
                                        <th>原始金额</th>
                                        <th>实际金额</th>
                                        <th>折扣金额</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="orderListTableBody">
                                    <!-- 订单列表将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div id="orderDetailContainer" class="order-detail mt-4" style="display: none;">
                        <h6>订单衣物详情</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>衣物名称</th>
                                        <th>颜色</th>
                                        <th>数量</th>
                                        <th>原价</th>
                                        <th>折扣率</th>
                                        <th>实际价格</th>
                                    </tr>
                                </thead>
                                <tbody id="clothingDetailTableBody">
                                    <!-- 衣物详情将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/flatpickr/4.6.9/flatpickr.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/flatpickr/4.6.9/l10n/zh.min.js"></script>

    <script>
    // 全局变量
    let currentCustomerId = null;
    let customers = [];
    let currentPage = 1;
    let totalPages = 1;
    let perPage = 10;

    // 页面加载完成后执行
    $(document).ready(function() {
        // 初始化日期选择器
        initDatePickers();

        // 加载区域列表
        loadAreas();

        // 加载商场客户列表
        loadCustomers();

        // 绑定搜索按钮事件
        $('#searchBtn').click(function() {
            currentPage = 1;
            loadCustomers();
        });

        // 绑定搜索输入框事件 - 防抖处理
        let searchTimeout;
        $('#searchInput').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                currentPage = 1;
                loadCustomers();
            }, 500); // 500ms防抖
        });

        // 绑定状态筛选事件
        $('#statusFilter').change(function() {
            currentPage = 1;
            loadCustomers();
        });

        // 绑定添加客户按钮事件
        $('#saveCustomerBtn').click(saveCustomer);

        // 绑定更新客户按钮事件
        $('#updateCustomerBtn').click(updateCustomer);

        // 绑定编辑客户按钮事件
        $('#editCustomerBtn').click(function() {
            $('#customerDetailModal').modal('hide');
            showEditCustomerModal(currentCustomerId);
        });

        // 绑定添加折扣按钮事件
        $('#addDiscountBtn').click(function() {
            showDiscountModal(currentCustomerId);
        });

        // 绑定保存折扣按钮事件
        $('#saveDiscountBtn').click(saveDiscount);

        // 绑定添加账单按钮事件
        $('#addBillBtn').click(function() {
            showBillModal(currentCustomerId);
        });

        // 绑定保存账单按钮事件
        $('#saveBillBtn').click(saveBill);

        // 绑定确认删除按钮事件
        $('#confirmDeleteBtn').click(confirmDelete);

        // 导出按钮点击事件
        $('#exportBillOrdersBtn').click(exportBillOrders);
    });

    // 初始化日期选择器
    function initDatePickers() {
        flatpickr(".date-picker", {
            dateFormat: "Y-m-d",
            locale: "zh",
            allowInput: true
        });
    }

    // 加载区域列表
    function loadAreas() {
        $.ajax({
            url: '/api/areas',
            method: 'GET',
            success: function(response) {
                const areas = response.areas || [];
                const areaSelects = ['#area', '#editArea'];

                areaSelects.forEach(selector => {
                    const select = $(selector);
                    select.empty();
                    select.append('<option value="">请选择区域</option>');

                    areas.forEach(area => {
                        select.append(`<option value="${area}">${area}</option>`);
                    });
                });

                // 如果当前用户有区域限制，自动选择并禁用
                const staffArea = '{{ session.get("staff_area", "") }}';
                const staffRole = '{{ session.get("staff_role", "") }}';

                // 超级管理员(admin)或总部区域可以选择任意区域，其他角色只能选择自己的区域
                if (staffRole !== 'admin' && staffArea !== '总部' && staffArea) {
                    areaSelects.forEach(selector => {
                        const select = $(selector);
                        select.val(staffArea);
                        if (selector === '#area') {
                            // 新建时自动设置为当前用户区域且不可修改
                            select.prop('disabled', true);
                        }
                    });
                }
            },
            error: function(xhr) {
                console.error('加载区域列表失败:', xhr.responseText);
                // 如果加载失败，至少设置当前用户的区域
                const staffArea = '{{ session.get("staff_area", "") }}';
                if (staffArea) {
                    const areaSelects = ['#area', '#editArea'];
                    areaSelects.forEach(selector => {
                        const select = $(selector);
                        select.empty();
                        select.append(`<option value="${staffArea}">${staffArea}</option>`);
                        select.val(staffArea);
                    });
                }
            }
        });
    }

    // 加载商场客户列表
    function loadCustomers() {
        const searchTerm = $('#searchInput').val();
        const status = $('#statusFilter').val();

        $.ajax({
            url: '/api/mall_customers',
            method: 'GET',
            data: {
                search: searchTerm,
                status: status,
                page: currentPage,
                per_page: perPage
            },
            success: function(response) {
                customers = response.customers;
                totalPages = response.pages;
                renderCustomers(customers);
                renderPagination(response);
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '加载商场客户列表失败';
                alert(errorMsg);
            }
        });
    }

    // 渲染商场客户列表
    function renderCustomers(customers) {
        const tableBody = $('#customerTableBody');
        tableBody.empty();

        if (customers.length === 0) {
            tableBody.html('<tr><td colspan="9" class="text-center">没有找到符合条件的商场客户</td></tr>');
            return;
        }

        customers.forEach(customer => {
            const statusClass = getStatusClass(customer.status);
            const billingStatusClass = getBillingStatusClass(customer.billing_status);

            const row = `
                <tr>
                    <td>${customer.mall_name}</td>
                    <td>${customer.contact_name || '-'}</td>
                    <td>${customer.phone || '-'}</td>
                    <td>${customer.billing_cycle}</td>
                    <td>${customer.contract_end_date || '无限期'}</td>
                    <td><span class="customer-status ${statusClass}">${customer.status}</span></td>
                    <td><span class="billing-status ${billingStatusClass}">${customer.billing_status}</span></td>
                    <td><span class="badge badge-discount">${customer.discount_count}</span></td>
                    <td>
                        <button class="action-btn view-btn" onclick="showCustomerDetail(${customer.id})">查看</button>
                        <button class="action-btn edit-btn" onclick="showEditCustomerModal(${customer.id})">编辑</button>
                        <button class="action-btn delete-btn" onclick="showDeleteConfirmModal(${customer.id}, 'customer')">删除</button>
                    </td>
                </tr>
            `;

            tableBody.append(row);
        });
    }

    // 获取状态对应的CSS类
    function getStatusClass(status) {
        switch (status) {
            case '活跃': return 'status-active';
            case '暂停': return 'status-paused';
            case '已终止': return 'status-terminated';
            default: return '';
        }
    }

    // 获取账单状态对应的CSS类
    function getBillingStatusClass(status) {
        switch (status) {
            case '已付款': return 'billing-paid';
            case '未付款': return 'billing-unpaid';
            case '部分付款': return 'billing-partial';
            default: return '';
        }
    }

    // 渲染分页控件
    function renderPagination(response) {
        const pagination = $('#pagination');
        pagination.empty();

        if (response.total === 0) {
            return;
        }

        // 上一页按钮
        pagination.append(`
            <button ${response.current_page === 1 ? 'disabled' : ''} onclick="goToPage(${response.current_page - 1})">上一页</button>
        `);

        // 页码按钮
        for (let i = 1; i <= response.pages; i++) {
            pagination.append(`
                <button class="${response.current_page === i ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>
            `);
        }

        // 下一页按钮
        pagination.append(`
            <button ${response.current_page === response.pages ? 'disabled' : ''} onclick="goToPage(${response.current_page + 1})">下一页</button>
        `);
    }

    // 跳转到指定页
    function goToPage(page) {
        if (page < 1 || page > totalPages) {
            return;
        }

        currentPage = page;
        loadCustomers();
    }

    // 显示商场客户详情
    function showCustomerDetail(customerId) {
        currentCustomerId = customerId;

        // 获取客户详情
        $.ajax({
            url: `/api/mall_customers/${customerId}`,
            method: 'GET',
            success: function(customer) {
                // 设置基本信息
                $('#detailMallName').text(customer.mall_name);
                $('#detailPhone').text(customer.phone || '-');
                $('#detailAddress').text(customer.address || '-');
                $('#detailContractDates').text(
                    (customer.contract_start_date ? customer.contract_start_date : '无限制') +
                    ' 至 ' +
                    (customer.contract_end_date ? customer.contract_end_date : '无限制')
                );
                $('#detailBillingCycle').text(customer.billing_cycle);
                $('#detailStatus').text(customer.status);
                $('#detailArea').text(customer.area || '-');
                $('#detailContactName').text(customer.contact_name || '-');
                $('#detailContactPhone').text(customer.contact_phone || '-');
                $('#detailContactPosition').text(customer.contact_position || '-');
                $('#detailOverallDiscountRate').text(
                    customer.overall_discount_rate
                        ? (customer.overall_discount_rate * 10).toFixed(1) + ' 折'
                        : '无折扣 (1.0倍)'
                );
                $('#detailRemarks').text(customer.remarks || '无');

                // 加载产品折扣列表
                loadCustomerDiscounts(customerId);

                // 加载月度账单列表
                loadCustomerBills(customerId);

                // 加载折扣历史记录
                loadDiscountHistory(customerId);

                // 显示模态框
                $('#customerDetailModal').modal('show');
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '获取商场客户详情失败';
                alert(errorMsg);
            }
        });
    }

    // 显示添加/编辑商场客户模态框
    function showAddCustomerModal() {
        // 重置表单
        $('#customerForm')[0].reset();

        // 修改标题
        $('#addCustomerModalLabel').text('添加商场客户');

        // 重新加载区域列表
        loadAreas();

        // 显示模态框
        $('#addCustomerModal').modal('show');
    }

    // 保存商场客户
    function saveCustomer() {
        const customerData = {
            mall_name: $('#mallName').val(),
            phone: $('#phone').val(),
            address: $('#address').val(),
            contract_start_date: $('#contractStartDate').val() || null,
            contract_end_date: $('#contractEndDate').val() || null,
            billing_cycle: $('#billingCycle').val(),
            status: $('#status').val(),
            area: $('#area').val(),
            contact_name: $('#contactName').val(),
            contact_phone: $('#contactPhone').val(),
            contact_position: $('#contactPosition').val(),
            remarks: $('#remarks').val()
        };

        $.ajax({
            url: '/api/mall_customers',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(customerData),
            success: function(response) {
                alert(response.message);
                $('#addCustomerModal').modal('hide');
                loadCustomers();
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '创建商场客户失败';
                alert(errorMsg);
            }
        });
    }

    // 显示编辑商场客户模态框
    function showEditCustomerModal(customerId) {
        // 先加载区域列表
        loadAreas();

        $.ajax({
            url: `/api/mall_customers/${customerId}`,
            method: 'GET',
            success: function(customer) {
                // 填充表单
                $('#editCustomerId').val(customer.id);
                $('#editMallName').val(customer.mall_name);
                $('#editPhone').val(customer.phone);
                $('#editAddress').val(customer.address);
                $('#editContractStartDate').val(customer.contract_start_date);
                $('#editContractEndDate').val(customer.contract_end_date);
                $('#editBillingCycle').val(customer.billing_cycle);
                $('#editStatus').val(customer.status);

                // 延迟设置区域值，确保区域列表已加载
                setTimeout(() => {
                    $('#editArea').val(customer.area);

                    // 检查权限，非管理员且非总部不能修改区域
                    const staffRole = '{{ session.get("staff_role", "") }}';
                    const staffArea = '{{ session.get("staff_area", "") }}';
                    if (staffRole !== 'admin' && staffArea !== '总部') {
                        $('#editArea').prop('disabled', true);
                    }
                }, 500);

                $('#editContactName').val(customer.contact_name);
                $('#editContactPhone').val(customer.contact_phone);
                $('#editContactPosition').val(customer.contact_position);
                $('#editOverallDiscountRate').val(customer.overall_discount_rate || 1.0);
                $('#editRemarks').val(customer.remarks);

                // 重新初始化日期选择器
                initDatePickers();

                // 显示模态框
                $('#editCustomerModal').modal('show');
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '获取商场客户信息失败';
                alert(errorMsg);
            }
        });
    }

    // 更新商场客户
    function updateCustomer() {
        const customerId = $('#editCustomerId').val();
        const customerData = {
            mall_name: $('#editMallName').val(),
            phone: $('#editPhone').val(),
            address: $('#editAddress').val(),
            contract_start_date: $('#editContractStartDate').val() || null,
            contract_end_date: $('#editContractEndDate').val() || null,
            billing_cycle: $('#editBillingCycle').val(),
            status: $('#editStatus').val(),
            area: $('#editArea').val(),
            contact_name: $('#editContactName').val(),
            contact_phone: $('#editContactPhone').val(),
            contact_position: $('#editContactPosition').val(),
            overall_discount_rate: $('#editOverallDiscountRate').val(),
            remarks: $('#editRemarks').val()
        };

        $.ajax({
            url: `/api/mall_customers/${customerId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(customerData),
            success: function(response) {
                alert(response.message);
                $('#editCustomerModal').modal('hide');
                loadCustomers();
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '更新商场客户失败';
                alert(errorMsg);
            }
        });
    }

    // 加载客户的产品折扣列表
    function loadCustomerDiscounts(customerId) {
        $.ajax({
            url: `/api/mall_customers/${customerId}/discounts`,
            method: 'GET',
            success: function(response) {
                renderDiscounts(response.discounts);
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '加载产品折扣列表失败';
                console.error(errorMsg);
            }
        });
    }

    // 渲染折扣列表
    function renderDiscounts(discounts) {
        const tableBody = $('#discountTableBody');
        tableBody.empty();

        if (discounts.length === 0) {
            tableBody.html('<tr><td colspan="6" class="text-center">暂无产品折扣</td></tr>');
            return;
        }

        discounts.forEach(discount => {
            const row = `
                <tr>
                    <td>${discount.product_name}</td>
                    <td>${discount.product_type}</td>
                    <td>${(discount.discount_rate * 10).toFixed(1)} 折</td>
                    <td>${discount.effective_date}</td>
                    <td>${discount.expiry_date}</td>
                    <td>
                        <button class="btn btn-sm btn-warning" onclick="showDiscountModal(${currentCustomerId}, ${discount.id})">编辑</button>
                        <button class="btn btn-sm btn-danger" onclick="showDeleteConfirmModal(${discount.id}, 'discount')">删除</button>
                    </td>
                </tr>
            `;

            tableBody.append(row);
        });
    }

    // 加载产品列表
    function loadProductList() {
        // 清空现有选项，保留第一个默认选项
        const productSelect = $('#productName');
        const defaultOption = productSelect.find('option:first');
        productSelect.empty().append(defaultOption);

        // 发送请求获取产品列表
        $.ajax({
            url: '/api/product_types',
            method: 'GET',
            success: function(response) {
                if (response.products && response.products.length > 0) {
                    // 按名称排序商品列表
                    const sortedProducts = response.products.sort((a, b) =>
                        a.name.localeCompare(b.name, 'zh-CN')
                    );

                    // 添加产品选项
                    sortedProducts.forEach(product => {
                        productSelect.append(`
                            <option value="${product.name}"
                                    data-category="${product.category}"
                                    data-price="${product.price}">
                                ${product.name} (${product.category}, ¥${product.price})
                            </option>
                        `);
                    });

                    // 商品名称变更时自动更新类型
                    productSelect.on('change', function() {
                        const selectedOption = $(this).find('option:selected');
                        const category = selectedOption.data('category');
                        if (category) {
                            $('#productType').val(category);
                        }
                    });
                }
            },
            error: function(xhr) {
                console.error('获取产品列表失败:', xhr.responseText);
                // 加载失败时显示一些默认产品
                const defaultProducts = ['衬衫', '西裤', '西装外套', '大衣', 'T恤', '裙子', '羽绒服'];
                defaultProducts.forEach(product => {
                    productSelect.append(`<option value="${product}">${product}</option>`);
                });
            }
        });
    }

    // 显示折扣模态框
    function showDiscountModal(customerId, discountId = null) {
        // 重置表单
        $('#discountForm')[0].reset();

        // 设置客户ID
        $('#discountCustomerId').val(customerId);

        // 加载产品列表
        loadProductList();

        if (discountId) {
            // 修改标题
            $('#discountModalLabel').text('编辑产品折扣');

            // 获取折扣详情
            $.ajax({
                url: `/api/discounts/${discountId}`,
                method: 'GET',
                success: function(discount) {
                    $('#discountId').val(discount.id);

                    // 等待产品列表加载完成后再设置值
                    setTimeout(() => {
                        $('#productName').val(discount.product_name);
                        $('#productType').val(discount.product_type);
                    }, 500);

                    $('#discountRate').val(discount.discount_rate);
                    $('#effectiveDate').val(discount.effective_date);
                    $('#expiryDate').val(discount.expiry_date !== '长期有效' ? discount.expiry_date : '');
                    $('#changeReason').val(discount.change_reason || '');

                    // 重新初始化日期选择器
                    initDatePickers();

                    // 显示模态框
                    $('#discountModal').modal('show');
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '获取折扣详情失败';
                    alert(errorMsg);
                }
            });
        } else {
            // 修改标题
            $('#discountModalLabel').text('添加产品折扣');

            // 设置默认值
            $('#discountId').val('');
            $('#effectiveDate').val(new Date().toISOString().split('T')[0]);
            $('#discountRate').val(1.0);
            $('#changeReason').val('');

            // 重新初始化日期选择器
            initDatePickers();

            // 显示模态框
            $('#discountModal').modal('show');
        }
    }

    // 保存折扣
    function saveDiscount() {
        const customerId = $('#discountCustomerId').val();
        const discountId = $('#discountId').val();

        const discountData = {
            product_name: $('#productName').val(),
            product_type: $('#productType').val(),
            discount_rate: $('#discountRate').val(),
            effective_date: $('#effectiveDate').val(),
            expiry_date: $('#expiryDate').val() || null,
            change_reason: $('#changeReason').val() || '定期调整'
        };

        if (discountId) {
            // 更新折扣
            $.ajax({
                url: `/api/discounts/${discountId}`,
                method: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(discountData),
                success: function(response) {
                    alert(response.message);
                    $('#discountModal').modal('hide');
                    loadCustomerDiscounts(customerId);
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '更新折扣失败';
                    alert(errorMsg);
                }
            });
        } else {
            // 创建新折扣
            $.ajax({
                url: `/api/mall_customers/${customerId}/discounts`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(discountData),
                success: function(response) {
                    alert(response.message);
                    $('#discountModal').modal('hide');
                    loadCustomerDiscounts(customerId);
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '创建折扣失败';
                    alert(errorMsg);
                }
            });
        }
    }

    // 加载客户的月度账单列表
    function loadCustomerBills(customerId) {
        $.ajax({
            url: `/api/mall_customers/${customerId}/bills`,
            method: 'GET',
            success: function(response) {
                renderBills(response.bills);
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '加载月度账单列表失败';
                console.error(errorMsg);
            }
        });
    }

    // 渲染账单列表
    function renderBills(bills) {
        const tableBody = $('#billTableBody');
        tableBody.empty();

        if (bills.length === 0) {
            tableBody.html('<tr><td colspan="10" class="text-center">暂无月度账单</td></tr>');
            return;
        }

        bills.forEach(bill => {
            const statusClass = getBillingStatusClass(bill.payment_status);

            const row = `
                <tr>
                    <td>${bill.bill_year_month}</td>
                    <td>${bill.bill_start_date} 至 ${bill.bill_end_date}</td>
                    <td>${bill.order_count}</td>
                    <td>${(bill.original_amount || 0).toFixed(2)} 元</td>
                    <td>${bill.total_amount.toFixed(2)} 元</td>
                    <td>${bill.discount_amount.toFixed(2)} 元</td>
                    <td>${bill.actual_amount.toFixed(2)} 元</td>
                    <td><span class="billing-status ${statusClass}">${bill.payment_status}</span></td>
                    <td>${bill.payment_date || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="showBillOrdersModal(${bill.id})">查看订单</button>
                        <button class="btn btn-sm btn-warning" onclick="showBillModal(${currentCustomerId}, ${bill.id})">编辑</button>
                        <button class="btn btn-sm btn-danger" onclick="showDeleteConfirmModal(${bill.id}, 'bill')">删除</button>
                    </td>
                </tr>
            `;

            tableBody.append(row);
        });
    }

    // 显示账单模态框
    function showBillModal(customerId, billId = null) {
        // 重置表单
        $('#billForm')[0].reset();

        // 设置客户ID
        $('#billCustomerId').val(customerId);

        if (billId) {
            // 修改标题
            $('#billModalLabel').text('编辑月度账单');

            // 获取账单详情
            $.ajax({
                url: `/api/bills/${billId}`,
                method: 'GET',
                success: function(bill) {
                    $('#billId').val(bill.id);
                    $('#billYearMonth').val(bill.bill_year_month);
                    $('#billStartDate').val(bill.bill_start_date);
                    $('#billEndDate').val(bill.bill_end_date);
                    $('#orderCount').val(bill.order_count);
                    $('#originalAmount').val(bill.original_amount || 0);
                    $('#totalAmount').val(bill.total_amount);
                    $('#discountAmount').val(bill.discount_amount);
                    $('#actualAmount').val(bill.actual_amount);
                    $('#paymentStatus').val(bill.payment_status);
                    $('#paymentDate').val(bill.payment_date);
                    $('#paymentMethod').val(bill.payment_method);
                    $('#billRemarks').val(bill.remarks);

                    // 重新初始化日期选择器
                    initDatePickers();

                    // 显示模态框
                    $('#billModal').modal('show');
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '获取账单详情失败';
                    alert(errorMsg);
                }
            });
        } else {
            // 修改标题
            $('#billModalLabel').text('添加月度账单');

            // 设置默认值
            $('#billId').val('');

            // 设置默认年月为当前月
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            $('#billYearMonth').val(`${year}-${month}`);

            // 计算当月的第一天和最后一天
            const firstDay = new Date(year, today.getMonth(), 1);
            const lastDay = new Date(year, today.getMonth() + 1, 0);

            $('#billStartDate').val(firstDay.toISOString().split('T')[0]);
            $('#billEndDate').val(lastDay.toISOString().split('T')[0]);

            // 重新初始化日期选择器
            initDatePickers();

            // 显示模态框
            $('#billModal').modal('show');
        }
    }

    // 保存账单
    function saveBill() {
        const customerId = $('#billCustomerId').val();
        const billId = $('#billId').val();

        const billData = {
            bill_year_month: $('#billYearMonth').val(),
            bill_start_date: $('#billStartDate').val(),
            bill_end_date: $('#billEndDate').val(),
            order_count: $('#orderCount').val() || 0,
            original_amount: $('#originalAmount').val() || 0,
            total_amount: $('#totalAmount').val() || 0,
            discount_amount: $('#discountAmount').val() || 0,
            actual_amount: $('#actualAmount').val() || 0,
            payment_status: $('#paymentStatus').val(),
            payment_date: $('#paymentDate').val() || null,
            payment_method: $('#paymentMethod').val(),
            remarks: $('#billRemarks').val()
        };

        if (billId) {
            // 更新账单
            $.ajax({
                url: `/api/bills/${billId}`,
                method: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(billData),
                success: function(response) {
                    alert(response.message);
                    $('#billModal').modal('hide');
                    loadCustomerBills(customerId);
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '更新账单失败';
                    alert(errorMsg);
                }
            });
        } else {
            // 创建新账单
            $.ajax({
                url: `/api/mall_customers/${customerId}/bills`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(billData),
                success: function(response) {
                    alert(response.message);
                    $('#billModal').modal('hide');
                    loadCustomerBills(customerId);
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '创建账单失败';
                    alert(errorMsg);
                }
            });
        }
    }

    // 显示账单订单详情模态框
    function showBillOrdersModal(billId) {
        // 清空现有内容
        $('#orderListTableBody').empty();
        $('#clothingDetailTableBody').empty();
        $('#orderDetailContainer').hide();

        // 设置导出按钮的账单ID
        $('#exportBillOrdersBtn').data('billId', billId);

        // 获取账单详情
        $.ajax({
            url: `/api/bills/${billId}`,
            method: 'GET',
            success: function(bill) {
                // 显示账单基本信息
                $('#orderBillPeriod').text(`${bill.bill_start_date} 至 ${bill.bill_end_date}`);
                $('#orderBillCount').text(bill.order_count);
                $('#orderBillOriginalAmount').text(`${(bill.original_amount || 0).toFixed(2)} 元`);
                $('#orderBillAmount').text(`${bill.total_amount.toFixed(2)} 元`);
                $('#orderBillDiscount').text(`${bill.discount_amount.toFixed(2)} 元`);
                $('#orderBillActualAmount').text(`${bill.actual_amount.toFixed(2)} 元`);

                // 加载订单列表
                $.ajax({
                    url: `/api/bills/${billId}/orders`,
                    method: 'GET',
                    success: function(response) {
                        const orders = response.orders;

                        if (orders.length === 0) {
                            $('#orderListTableBody').html('<tr><td colspan="9" class="text-center">暂无关联订单</td></tr>');
                        } else {
                            orders.forEach(order => {
                                // 使用后端计算好的正确金额数据
                                // original_amount: 原始总金额（折扣前）
                                // total_amount: 实际总金额（折扣后）
                                // discount_amount: 折扣金额
                                const originalAmount = order.original_amount || 0;
                                const actualAmount = order.total_amount || 0;
                                const discountAmount = order.discount_amount || 0;

                                const row = `
                                    <tr>
                                        <td>${order.order_number}</td>
                                        <td>${order.created_at}</td>
                                        <td>${order.status}</td>
                                        <td>${order.payment_status}</td>
                                        <td>${order.clothing_count}</td>
                                        <td>${originalAmount.toFixed(2)} 元</td>
                                        <td>${actualAmount.toFixed(2)} 元</td>
                                        <td>${discountAmount.toFixed(2)} 元</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="showOrderDetails(${JSON.stringify(order).replace(/"/g, '&quot;')})">查看衣物</button>
                                        </td>
                                    </tr>
                                `;

                                $('#orderListTableBody').append(row);
                            });
                        }
                    },
                    error: function(xhr) {
                        const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '加载订单数据失败';
                        $('#orderListTableBody').html(`<tr><td colspan="9" class="text-center text-danger">${errorMsg}</td></tr>`);
                    }
                });

                // 显示模态框
                $('#billOrdersModal').modal('show');
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '获取账单详情失败';
                alert(errorMsg);
            }
        });
    }

    // 显示订单衣物详情
    function showOrderDetails(order) {
        $('#clothingDetailTableBody').empty();

        if (!order.clothing_items || order.clothing_items.length === 0) {
            $('#clothingDetailTableBody').html('<tr><td colspan="6" class="text-center">暂无衣物数据</td></tr>');
        } else {
            order.clothing_items.forEach(item => {
                // 使用后端返回的正确价格数据
                const originalPrice = item.original_price || 0;  // 原始单价
                const actualPrice = item.price || 0;             // 实际单价（折扣后）
                const quantity = item.quantity || 1;
                const discountRate = item.discount_rate || 1.0;

                // 计算折扣显示（如果有折扣）
                let discountDisplay = '无折扣';
                if (originalPrice > actualPrice && originalPrice > 0) {
                    const discountPercent = (actualPrice / originalPrice * 10).toFixed(1);
                    discountDisplay = `${discountPercent} 折`;
                }

                const row = `
                    <tr>
                        <td>${item.name}</td>
                        <td>${item.color}</td>
                        <td>${quantity}</td>
                        <td>${originalPrice.toFixed(2)} 元</td>
                        <td>${discountDisplay}</td>
                        <td>${actualPrice.toFixed(2)} 元</td>
                    </tr>
                `;

                $('#clothingDetailTableBody').append(row);
            });
        }

        // 显示衣物详情区域
        $('#orderDetailContainer').show();
    }

    // 加载折扣变更历史
    function loadDiscountHistory(customerId) {
        $.ajax({
            url: `/api/mall_customers/${customerId}/discount_history`,
            method: 'GET',
            success: function(response) {
                renderDiscountHistory(response.history);
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '加载折扣变更历史失败';
                console.error(errorMsg);
            }
        });
    }

    // 渲染折扣变更历史
    function renderDiscountHistory(history) {
        const tableBody = $('#historyTableBody');
        tableBody.empty();

        if (history.length === 0) {
            tableBody.html('<tr><td colspan="7" class="text-center">暂无折扣变更历史</td></tr>');
            return;
        }

        history.forEach(record => {
            const row = `
                <tr>
                    <td>${record.product_name}</td>
                    <td>${record.product_type}</td>
                    <td>${record.old_discount_rate ? (record.old_discount_rate * 10).toFixed(1) + ' 折' : '-'}</td>
                    <td>${(record.new_discount_rate * 10).toFixed(1)} 折</td>
                    <td>${record.change_date}</td>
                    <td>${record.change_reason || '-'}</td>
                    <td>${record.operator}</td>
                </tr>
            `;

            tableBody.append(row);
        });
    }

    // 显示删除确认模态框
    function showDeleteConfirmModal(itemId, itemType) {
        $('#deleteItemId').val(itemId);
        $('#deleteItemType').val(itemType);
        $('#deleteModal').modal('show');
    }

    // 确认删除
    function confirmDelete() {
        const itemId = $('#deleteItemId').val();
        const itemType = $('#deleteItemType').val();

        let url = '';
        let successCallback = null;

        switch (itemType) {
            case 'customer':
                url = `/api/mall_customers/${itemId}`;
                successCallback = loadCustomers;
                break;
            case 'discount':
                url = `/api/discounts/${itemId}`;
                successCallback = function() { loadCustomerDiscounts(currentCustomerId); };
                break;
            case 'bill':
                url = `/api/bills/${itemId}`;
                successCallback = function() { loadCustomerBills(currentCustomerId); };
                break;
            default:
                alert('未知的删除类型');
                return;
        }

        $.ajax({
            url: url,
            method: 'DELETE',
            success: function(response) {
                alert(response.message);
                $('#deleteModal').modal('hide');
                successCallback();

                // 如果删除的是当前客户，关闭详情模态框
                if (itemType === 'customer' && itemId === currentCustomerId) {
                    $('#customerDetailModal').modal('hide');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : '删除失败';
                alert(errorMsg);
            }
        });
    }

    // 导出账单订单明细
    function exportBillOrders() {
        const billId = $('#exportBillOrdersBtn').data('billId');
        if (!billId) {
            alert('无法获取账单ID');
            return;
        }

        // 显示加载状态
        const originalText = $('#exportBillOrdersBtn').html();
        $('#exportBillOrdersBtn').html('<i class="fas fa-spinner fa-spin"></i> 导出中...');
        $('#exportBillOrdersBtn').prop('disabled', true);

        // 创建一个隐藏的链接来下载文件
        const link = document.createElement('a');
        link.href = `/api/bills/${billId}/export`;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 恢复按钮状态
        setTimeout(() => {
            $('#exportBillOrdersBtn').html(originalText);
            $('#exportBillOrdersBtn').prop('disabled', false);
        }, 2000);
    }
    </script>
</body>
</html>
